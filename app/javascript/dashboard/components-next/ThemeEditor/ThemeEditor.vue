<template>
  <div class="theme-editor">
    <!-- 主题编辑器头部 -->
    <div class="theme-editor-header">
      <h2 class="text-lg font-semibold text-n-slate-12">主题编辑器</h2>
      <div class="flex gap-2">
        <button
          @click="resetTheme"
          class="px-3 py-1 text-sm bg-n-solid-2 hover:bg-n-solid-3 rounded-md"
        >
          重置
        </button>
        <button
          @click="exportTheme"
          class="px-3 py-1 text-sm bg-n-iris-9 text-white rounded-md hover:bg-n-iris-10"
        >
          导出
        </button>
      </div>
    </div>

    <!-- 主题预设 -->
    <div class="theme-presets mb-6">
      <h3 class="text-sm font-medium text-n-slate-11 mb-3">预设主题</h3>
      <div class="grid grid-cols-3 gap-2">
        <button
          v-for="preset in themePresets"
          :key="preset.name"
          @click="applyPreset(preset)"
          class="preset-card"
          :class="{ active: currentPreset === preset.name }"
        >
          <div class="preset-colors">
            <div
              v-for="color in preset.colors"
              :key="color"
              class="w-4 h-4 rounded-full"
              :style="{ backgroundColor: color }"
            ></div>
          </div>
          <span class="text-xs">{{ preset.name }}</span>
        </button>
      </div>
    </div>

    <!-- 颜色编辑器 -->
    <div class="color-editor">
      <h3 class="text-sm font-medium text-n-slate-11 mb-3">颜色配置</h3>
      <div class="space-y-4">
        <div
          v-for="(colorGroup, groupName) in colorGroups"
          :key="groupName"
          class="color-group"
        >
          <h4 class="text-xs font-medium text-n-slate-10 mb-2 capitalize">
            {{ groupName }}
          </h4>
          <div class="grid grid-cols-2 gap-3">
            <div
              v-for="(color, colorName) in colorGroup"
              :key="colorName"
              class="color-item"
            >
              <label class="text-xs text-n-slate-11">{{ colorName }}</label>
              <div class="flex items-center gap-2">
                <input
                  type="color"
                  :value="color.hex"
                  @input="updateColor(groupName, colorName, $event.target.value)"
                  class="w-8 h-8 rounded border border-n-weak cursor-pointer"
                />
                <div class="flex-1">
                  <input
                    type="text"
                    :value="color.oklch"
                    @input="updateColorFromOKLCH(groupName, colorName, $event.target.value)"
                    placeholder="oklch(0.7 0.15 250)"
                    class="w-full px-2 py-1 text-xs border border-n-weak rounded"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时预览 -->
    <div class="theme-preview mt-6">
      <h3 class="text-sm font-medium text-n-slate-11 mb-3">实时预览</h3>
      <div class="preview-container p-4 border border-n-weak rounded-lg">
        <!-- 预览组件 -->
        <div class="space-y-4">
          <!-- 按钮预览 -->
          <div class="flex gap-2">
            <button class="px-3 py-2 bg-primary text-primary-foreground rounded-md">
              主要按钮
            </button>
            <button class="px-3 py-2 bg-secondary text-secondary-foreground rounded-md">
              次要按钮
            </button>
            <button class="px-3 py-2 bg-destructive text-destructive-foreground rounded-md">
              危险按钮
            </button>
          </div>
          
          <!-- 卡片预览 -->
          <div class="bg-card text-card-foreground p-4 rounded-lg border border-border">
            <h4 class="font-medium mb-2">卡片标题</h4>
            <p class="text-muted-foreground text-sm">
              这是一个卡片内容的示例，用于预览主题效果。
            </p>
          </div>
          
          <!-- 输入框预览 -->
          <div class="space-y-2">
            <input
              type="text"
              placeholder="输入框示例"
              class="w-full px-3 py-2 bg-background border border-input rounded-md focus:ring-2 focus:ring-ring"
            />
            <select class="w-full px-3 py-2 bg-background border border-input rounded-md">
              <option>选择选项</option>
              <option>选项 1</option>
              <option>选项 2</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- 代码导出弹窗 -->
    <div v-if="showExportModal" class="export-modal">
      <div class="modal-backdrop" @click="showExportModal = false"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="text-lg font-semibold">导出主题代码</h3>
          <button @click="showExportModal = false" class="text-n-slate-11 hover:text-n-slate-12">
            ✕
          </button>
        </div>
        <div class="modal-body">
          <div class="tabs">
            <button
              v-for="tab in exportTabs"
              :key="tab.key"
              @click="activeExportTab = tab.key"
              class="tab-button"
              :class="{ active: activeExportTab === tab.key }"
            >
              {{ tab.label }}
            </button>
          </div>
          <div class="code-container">
            <pre class="code-block"><code>{{ exportCode }}</code></pre>
            <button @click="copyCode" class="copy-button">复制代码</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import { tweakcnThemeConfig, generateCSSVariables, themeManager } from '../../../theme/tweakcn-integration.js';

export default {
  name: 'ThemeEditor',
  setup() {
    const currentPreset = ref('default');
    const showExportModal = ref(false);
    const activeExportTab = ref('css');
    
    // 主题预设
    const themePresets = ref([
      {
        name: 'default',
        colors: ['#2781F6', '#E5E7EB', '#F9FAFB', '#111827'],
        config: tweakcnThemeConfig.colors
      },
      {
        name: 'ocean',
        colors: ['#0EA5E9', '#0F766E', '#F0F9FF', '#164E63'],
        config: {
          primary: { light: 'oklch(0.65 0.2 220)', dark: 'oklch(0.6 0.2 220)' },
          // ... 其他颜色配置
        }
      },
      {
        name: 'forest',
        colors: ['#059669', '#065F46', '#F0FDF4', '#14532D'],
        config: {
          primary: { light: 'oklch(0.6 0.15 150)', dark: 'oklch(0.55 0.15 150)' },
          // ... 其他颜色配置
        }
      }
    ]);
    
    // 当前颜色配置
    const colorGroups = reactive({
      primary: {
        primary: { hex: '#2781F6', oklch: 'oklch(0.7 0.15 250)' },
        'primary-foreground': { hex: '#FFFFFF', oklch: 'oklch(0.98 0.02 250)' }
      },
      secondary: {
        secondary: { hex: '#F1F5F9', oklch: 'oklch(0.95 0.02 250)' },
        'secondary-foreground': { hex: '#0F172A', oklch: 'oklch(0.15 0.02 250)' }
      },
      destructive: {
        destructive: { hex: '#EF4444', oklch: 'oklch(0.6 0.2 25)' },
        'destructive-foreground': { hex: '#FFFFFF', oklch: 'oklch(0.98 0.02 25)' }
      },
      neutral: {
        background: { hex: '#FFFFFF', oklch: 'oklch(0.99 0.01 250)' },
        foreground: { hex: '#0F172A', oklch: 'oklch(0.15 0.02 250)' },
        muted: { hex: '#F1F5F9', oklch: 'oklch(0.95 0.02 250)' },
        'muted-foreground': { hex: '#64748B', oklch: 'oklch(0.5 0.02 250)' }
      }
    });
    
    // 导出标签页
    const exportTabs = ref([
      { key: 'css', label: 'CSS Variables' },
      { key: 'tailwind', label: 'Tailwind Config' },
      { key: 'js', label: 'JavaScript Config' }
    ]);
    
    // 导出代码
    const exportCode = computed(() => {
      switch (activeExportTab.value) {
        case 'css':
          return generateCSSCode();
        case 'tailwind':
          return generateTailwindCode();
        case 'js':
          return generateJSCode();
        default:
          return '';
      }
    });
    
    // 方法
    const updateColor = (groupName, colorName, hexValue) => {
      colorGroups[groupName][colorName].hex = hexValue;
      // 这里应该添加hex到OKLCH的转换逻辑
      applyThemeChanges();
    };
    
    const updateColorFromOKLCH = (groupName, colorName, oklchValue) => {
      colorGroups[groupName][colorName].oklch = oklchValue;
      // 这里应该添加OKLCH到hex的转换逻辑
      applyThemeChanges();
    };
    
    const applyPreset = (preset) => {
      currentPreset.value = preset.name;
      // 应用预设配置
      Object.assign(colorGroups, preset.config);
      applyThemeChanges();
    };
    
    const resetTheme = () => {
      applyPreset(themePresets.value[0]);
    };
    
    const exportTheme = () => {
      showExportModal.value = true;
    };
    
    const applyThemeChanges = () => {
      // 实时应用主题变更到页面
      const root = document.documentElement;
      Object.entries(colorGroups).forEach(([groupName, colors]) => {
        Object.entries(colors).forEach(([colorName, colorValue]) => {
          root.style.setProperty(`--${colorName}`, colorValue.oklch);
        });
      });
    };
    
    const generateCSSCode = () => {
      let css = ':root {\n';
      Object.entries(colorGroups).forEach(([groupName, colors]) => {
        Object.entries(colors).forEach(([colorName, colorValue]) => {
          css += `  --${colorName}: ${colorValue.oklch};\n`;
        });
      });
      css += '}\n\n.dark {\n';
      // 添加暗色主题变量
      css += '}';
      return css;
    };
    
    const generateTailwindCode = () => {
      return `module.exports = {
  theme: {
    extend: {
      colors: {
        ${Object.entries(colorGroups).map(([groupName, colors]) => 
          Object.entries(colors).map(([colorName, colorValue]) => 
            `'${colorName}': 'hsl(var(--${colorName}))'`
          ).join(',\n        ')
        ).join(',\n        ')}
      }
    }
  }
}`;
    };
    
    const generateJSCode = () => {
      return `export const themeConfig = ${JSON.stringify(colorGroups, null, 2)};`;
    };
    
    const copyCode = async () => {
      try {
        await navigator.clipboard.writeText(exportCode.value);
        // 显示复制成功提示
      } catch (err) {
        console.error('复制失败:', err);
      }
    };
    
    onMounted(() => {
      // 初始化主题
      themeManager.init();
    });
    
    return {
      currentPreset,
      showExportModal,
      activeExportTab,
      themePresets,
      colorGroups,
      exportTabs,
      exportCode,
      updateColor,
      updateColorFromOKLCH,
      applyPreset,
      resetTheme,
      exportTheme,
      copyCode
    };
  }
};
</script>

<style scoped>
.theme-editor {
  @apply max-w-4xl mx-auto p-6;
}

.theme-editor-header {
  @apply flex justify-between items-center mb-6;
}

.preset-card {
  @apply p-3 border border-n-weak rounded-lg hover:border-n-iris-9 transition-colors;
}

.preset-card.active {
  @apply border-n-iris-9 bg-n-solid-iris;
}

.preset-colors {
  @apply flex gap-1 mb-2;
}

.color-group {
  @apply p-4 border border-n-weak rounded-lg;
}

.color-item {
  @apply space-y-1;
}

.export-modal {
  @apply fixed inset-0 z-50 flex items-center justify-center;
}

.modal-backdrop {
  @apply absolute inset-0 bg-black bg-opacity-50;
}

.modal-content {
  @apply relative bg-n-solid-1 rounded-lg shadow-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden;
}

.modal-header {
  @apply flex justify-between items-center p-4 border-b border-n-weak;
}

.modal-body {
  @apply p-4;
}

.tabs {
  @apply flex border-b border-n-weak mb-4;
}

.tab-button {
  @apply px-4 py-2 text-sm border-b-2 border-transparent hover:border-n-iris-9;
}

.tab-button.active {
  @apply border-n-iris-9 text-n-iris-9;
}

.code-container {
  @apply relative;
}

.code-block {
  @apply bg-n-solid-2 p-4 rounded-lg text-sm overflow-auto max-h-96;
}

.copy-button {
  @apply absolute top-2 right-2 px-2 py-1 text-xs bg-n-solid-3 hover:bg-n-solid-active rounded;
}
</style>
