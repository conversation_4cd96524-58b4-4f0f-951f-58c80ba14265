<template>
  <div class="theme-toggle">
    <!-- 主题切换按钮 -->
    <button
      @click="toggleTheme"
      class="theme-toggle-button"
      :title="getThemeTitle()"
      aria-label="切换主题"
    >
      <component :is="currentThemeIcon" class="w-4 h-4" />
    </button>
    
    <!-- 主题选择下拉菜单 -->
    <div v-if="showDropdown" class="theme-dropdown">
      <div class="dropdown-backdrop" @click="showDropdown = false"></div>
      <div class="dropdown-content">
        <button
          v-for="theme in themes"
          :key="theme.value"
          @click="setTheme(theme.value)"
          class="dropdown-item"
          :class="{ active: currentTheme === theme.value }"
        >
          <component :is="theme.icon" class="w-4 h-4" />
          <span>{{ theme.label }}</span>
          <div v-if="currentTheme === theme.value" class="check-icon">✓</div>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { themeManager } from '../../../theme/tweakcn-integration.js';

// 图标组件（简化版，实际项目中可能使用图标库）
const SunIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <circle cx="12" cy="12" r="5"/>
      <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
    </svg>
  `
};

const MoonIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
    </svg>
  `
};

const SystemIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
      <line x1="8" y1="21" x2="16" y2="21"/>
      <line x1="12" y1="17" x2="12" y2="21"/>
    </svg>
  `
};

export default {
  name: 'ThemeToggle',
  components: {
    SunIcon,
    MoonIcon,
    SystemIcon
  },
  props: {
    showLabel: {
      type: Boolean,
      default: false
    },
    variant: {
      type: String,
      default: 'button', // 'button' | 'dropdown'
      validator: (value) => ['button', 'dropdown'].includes(value)
    }
  },
  setup(props) {
    const currentTheme = ref('system');
    const showDropdown = ref(false);
    
    // 主题选项
    const themes = ref([
      {
        value: 'light',
        label: '浅色主题',
        icon: 'SunIcon'
      },
      {
        value: 'dark',
        label: '深色主题',
        icon: 'MoonIcon'
      },
      {
        value: 'system',
        label: '跟随系统',
        icon: 'SystemIcon'
      }
    ]);
    
    // 当前主题图标
    const currentThemeIcon = computed(() => {
      const theme = themes.value.find(t => t.value === currentTheme.value);
      return theme ? theme.icon : 'SystemIcon';
    });
    
    // 获取主题标题
    const getThemeTitle = () => {
      const theme = themes.value.find(t => t.value === currentTheme.value);
      return theme ? `当前: ${theme.label}` : '切换主题';
    };
    
    // 切换主题（循环切换）
    const toggleTheme = () => {
      if (props.variant === 'dropdown') {
        showDropdown.value = !showDropdown.value;
        return;
      }
      
      const currentIndex = themes.value.findIndex(t => t.value === currentTheme.value);
      const nextIndex = (currentIndex + 1) % themes.value.length;
      const nextTheme = themes.value[nextIndex].value;
      
      setTheme(nextTheme);
    };
    
    // 设置主题
    const setTheme = (theme) => {
      currentTheme.value = theme;
      themeManager.setTheme(theme);
      showDropdown.value = false;
      
      // 触发自定义事件
      document.dispatchEvent(new CustomEvent('theme-changed', {
        detail: { theme }
      }));
    };
    
    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = (e) => {
      if (currentTheme.value === 'system') {
        // 系统主题变化时重新应用主题
        themeManager.setTheme('system');
      }
    };
    
    // 初始化主题
    const initTheme = () => {
      const savedTheme = themeManager.getTheme();
      currentTheme.value = savedTheme;
      themeManager.setTheme(savedTheme);
    };
    
    // 点击外部关闭下拉菜单
    const handleClickOutside = (event) => {
      if (!event.target.closest('.theme-toggle')) {
        showDropdown.value = false;
      }
    };
    
    onMounted(() => {
      initTheme();
      mediaQuery.addEventListener('change', handleSystemThemeChange);
      document.addEventListener('click', handleClickOutside);
    });
    
    onUnmounted(() => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
      document.removeEventListener('click', handleClickOutside);
    });
    
    return {
      currentTheme,
      showDropdown,
      themes,
      currentThemeIcon,
      getThemeTitle,
      toggleTheme,
      setTheme
    };
  }
};
</script>

<style scoped>
.theme-toggle {
  @apply relative;
}

.theme-toggle-button {
  @apply p-2 rounded-md border border-border bg-background hover:bg-accent hover:text-accent-foreground transition-colors;
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
}

.theme-dropdown {
  @apply absolute top-full right-0 mt-2 z-50;
}

.dropdown-backdrop {
  @apply fixed inset-0;
}

.dropdown-content {
  @apply bg-popover text-popover-foreground border border-border rounded-md shadow-lg py-1 min-w-[160px];
}

.dropdown-item {
  @apply w-full flex items-center gap-2 px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground transition-colors;
  @apply focus:outline-none focus:bg-accent focus:text-accent-foreground;
}

.dropdown-item.active {
  @apply bg-accent text-accent-foreground;
}

.check-icon {
  @apply ml-auto text-primary;
}

/* 主题切换动画 */
.theme-toggle-button {
  transition: all 0.2s ease-in-out;
}

.theme-toggle-button:hover {
  transform: scale(1.05);
}

/* 深色主题下的特殊样式 */
:global(.dark) .theme-toggle-button {
  @apply border-border;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .dropdown-content {
    @apply right-0 left-auto;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .theme-toggle-button {
    @apply border-2;
  }
  
  .dropdown-item {
    @apply border-b border-border last:border-b-0;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .theme-toggle-button {
    transition: none;
  }
  
  .theme-toggle-button:hover {
    transform: none;
  }
}
</style>
