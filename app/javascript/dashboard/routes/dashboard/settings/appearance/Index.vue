<template>
  <div class="appearance-settings">
    <!-- 页面头部 -->
    <div class="settings-header">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-semibold text-n-slate-12">外观设置</h1>
          <p class="text-sm text-n-slate-11 mt-1">
            自定义Chatwoot的外观和主题，支持实时预览和导出配置
          </p>
        </div>
        <div class="flex gap-3">
          <button
            @click="resetToDefault"
            class="px-4 py-2 text-sm border border-n-weak rounded-md hover:bg-n-solid-2"
          >
            重置默认
          </button>
          <button
            @click="exportTheme"
            class="px-4 py-2 text-sm bg-n-iris-9 text-white rounded-md hover:bg-n-iris-10"
          >
            导出主题
          </button>
        </div>
      </div>
    </div>

    <div class="settings-content grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧：主题配置 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 基础主题选择 -->
        <div class="settings-section">
          <h2 class="section-title">基础主题</h2>
          <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
            <button
              v-for="mode in themeModes"
              :key="mode.value"
              @click="setThemeMode(mode.value)"
              class="theme-mode-card"
              :class="{ active: currentThemeMode === mode.value }"
            >
              <div class="mode-preview" :class="mode.previewClass">
                <div class="preview-header"></div>
                <div class="preview-content">
                  <div class="preview-sidebar"></div>
                  <div class="preview-main"></div>
                </div>
              </div>
              <div class="mode-info">
                <component :is="mode.icon" class="w-4 h-4" />
                <span class="text-sm font-medium">{{ mode.label }}</span>
              </div>
            </button>
          </div>
        </div>

        <!-- 主题预设 -->
        <div class="settings-section">
          <h2 class="section-title">主题预设</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              v-for="preset in themePresets"
              :key="preset.name"
              @click="applyPreset(preset.name)"
              class="preset-card"
              :class="{ active: currentPreset === preset.name }"
            >
              <div class="preset-colors">
                <div
                  v-for="(color, index) in preset.colors"
                  :key="index"
                  class="color-dot"
                  :style="{ backgroundColor: color }"
                ></div>
              </div>
              <div class="preset-info">
                <h3 class="font-medium">{{ preset.displayName }}</h3>
                <p class="text-xs text-n-slate-11">{{ preset.description }}</p>
              </div>
            </button>
          </div>
        </div>

        <!-- 高级颜色编辑 -->
        <div class="settings-section">
          <div class="flex items-center justify-between mb-4">
            <h2 class="section-title">高级颜色编辑</h2>
            <button
              @click="showAdvancedEditor = !showAdvancedEditor"
              class="text-sm text-n-iris-9 hover:text-n-iris-10"
            >
              {{ showAdvancedEditor ? '收起' : '展开' }}
            </button>
          </div>
          
          <div v-if="showAdvancedEditor" class="advanced-editor">
            <ThemeEditor />
          </div>
        </div>

        <!-- 导入/导出 -->
        <div class="settings-section">
          <h2 class="section-title">导入/导出</h2>
          <div class="flex gap-4">
            <div class="flex-1">
              <label class="block text-sm font-medium text-n-slate-11 mb-2">
                导入主题配置
              </label>
              <input
                ref="fileInput"
                type="file"
                accept=".json"
                @change="importTheme"
                class="hidden"
              />
              <button
                @click="$refs.fileInput.click()"
                class="w-full px-4 py-2 border border-n-weak rounded-md hover:bg-n-solid-2"
              >
                选择文件
              </button>
            </div>
            <div class="flex-1">
              <label class="block text-sm font-medium text-n-slate-11 mb-2">
                分享主题
              </label>
              <button
                @click="shareTheme"
                class="w-full px-4 py-2 bg-n-solid-2 rounded-md hover:bg-n-solid-3"
              >
                生成分享链接
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：实时预览 -->
      <div class="preview-panel">
        <div class="sticky top-6">
          <h2 class="section-title mb-4">实时预览</h2>
          <div class="preview-container">
            <!-- 预览内容 -->
            <div class="preview-app">
              <!-- 顶部导航栏 -->
              <div class="preview-header bg-background border-b border-border">
                <div class="flex items-center justify-between p-3">
                  <div class="flex items-center gap-2">
                    <div class="w-6 h-6 bg-primary rounded"></div>
                    <span class="font-medium text-foreground">Chatwoot</span>
                  </div>
                  <ThemeToggle variant="button" />
                </div>
              </div>

              <!-- 侧边栏 -->
              <div class="preview-content flex">
                <div class="preview-sidebar bg-sidebar border-r border-sidebar-border p-3">
                  <div class="space-y-2">
                    <div class="w-full h-8 bg-sidebar-accent rounded"></div>
                    <div class="w-3/4 h-6 bg-sidebar-accent rounded"></div>
                    <div class="w-full h-6 bg-sidebar-accent rounded"></div>
                  </div>
                </div>

                <!-- 主内容区 -->
                <div class="preview-main flex-1 p-4 bg-background">
                  <!-- 卡片示例 -->
                  <div class="bg-card border border-border rounded-lg p-4 mb-4">
                    <h3 class="font-medium text-card-foreground mb-2">示例卡片</h3>
                    <p class="text-muted-foreground text-sm">
                      这是一个示例卡片，展示当前主题的效果。
                    </p>
                  </div>

                  <!-- 按钮示例 -->
                  <div class="flex gap-2 mb-4">
                    <button class="px-3 py-2 bg-primary text-primary-foreground rounded-md text-sm">
                      主要按钮
                    </button>
                    <button class="px-3 py-2 bg-secondary text-secondary-foreground rounded-md text-sm">
                      次要按钮
                    </button>
                    <button class="px-3 py-2 bg-destructive text-destructive-foreground rounded-md text-sm">
                      危险按钮
                    </button>
                  </div>

                  <!-- 输入框示例 -->
                  <div class="space-y-2">
                    <input
                      type="text"
                      placeholder="输入框示例"
                      class="w-full px-3 py-2 bg-background border border-input rounded-md"
                    />
                    <textarea
                      placeholder="文本域示例"
                      rows="3"
                      class="w-full px-3 py-2 bg-background border border-input rounded-md resize-none"
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导出弹窗 -->
    <div v-if="showExportModal" class="export-modal">
      <div class="modal-backdrop" @click="showExportModal = false"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="text-lg font-semibold">导出主题配置</h3>
          <button @click="showExportModal = false" class="text-n-slate-11 hover:text-n-slate-12">
            ✕
          </button>
        </div>
        <div class="modal-body">
          <div class="mb-4">
            <label class="block text-sm font-medium text-n-slate-11 mb-2">
              主题名称
            </label>
            <input
              v-model="exportThemeName"
              type="text"
              class="w-full px-3 py-2 border border-n-weak rounded-md"
              placeholder="我的自定义主题"
            />
          </div>
          <div class="flex gap-2">
            <button
              @click="downloadTheme"
              class="flex-1 px-4 py-2 bg-n-iris-9 text-white rounded-md hover:bg-n-iris-10"
            >
              下载配置文件
            </button>
            <button
              @click="copyThemeConfig"
              class="flex-1 px-4 py-2 border border-n-weak rounded-md hover:bg-n-solid-2"
            >
              复制配置
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import { enhancedThemeManager, themePresets, colorUtils } from '../../../../theme/tweakcn-integration.js';
import ThemeEditor from '../../../components-next/ThemeEditor/ThemeEditor.vue';
import ThemeToggle from '../../../components-next/ThemeToggle/ThemeToggle.vue';

// 图标组件
const SunIcon = {
  template: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <circle cx="12" cy="12" r="5"/><path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
  </svg>`
};

const MoonIcon = {
  template: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
  </svg>`
};

const SystemIcon = {
  template: `<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/>
  </svg>`
};

export default {
  name: 'AppearanceSettings',
  components: {
    ThemeEditor,
    ThemeToggle,
    SunIcon,
    MoonIcon,
    SystemIcon
  },
  setup() {
    const currentThemeMode = ref('system');
    const currentPreset = ref('default');
    const showAdvancedEditor = ref(false);
    const showExportModal = ref(false);
    const exportThemeName = ref('');

    // 主题模式选项
    const themeModes = ref([
      {
        value: 'light',
        label: '浅色',
        icon: 'SunIcon',
        previewClass: 'light-preview'
      },
      {
        value: 'dark',
        label: '深色',
        icon: 'MoonIcon',
        previewClass: 'dark-preview'
      },
      {
        value: 'system',
        label: '跟随系统',
        icon: 'SystemIcon',
        previewClass: 'system-preview'
      }
    ]);

    // 处理后的主题预设
    const processedThemePresets = computed(() => {
      return Object.entries(themePresets).map(([key, preset]) => ({
        name: key,
        displayName: preset.name,
        description: `${preset.name}风格的配色方案`,
        colors: [
          preset.colors.primary.light,
          preset.colors.secondary.light,
          preset.colors.destructive.light,
          preset.colors.background.light
        ].map(color => {
          // 简化的OKLCH到hex转换（实际应用中需要更精确的转换）
          const parsed = colorUtils.parseOKLCH(color);
          if (parsed) {
            const hue = parsed.h;
            const lightness = Math.round(parsed.l * 100);
            const chroma = Math.round(parsed.c * 100);
            return `hsl(${hue}, ${chroma}%, ${lightness}%)`;
          }
          return color;
        })
      }));
    });

    // 方法
    const setThemeMode = (mode) => {
      currentThemeMode.value = mode;
      enhancedThemeManager.setTheme(mode);
    };

    const applyPreset = (presetName) => {
      currentPreset.value = presetName;
      enhancedThemeManager.applyPreset(presetName);
    };

    const resetToDefault = () => {
      setThemeMode('system');
      applyPreset('default');
    };

    const exportTheme = () => {
      exportThemeName.value = `主题_${new Date().toLocaleDateString()}`;
      showExportModal.value = true;
    };

    const downloadTheme = () => {
      const config = enhancedThemeManager.exportTheme();
      config.name = exportThemeName.value;
      
      const blob = new Blob([JSON.stringify(config, null, 2)], {
        type: 'application/json'
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${exportThemeName.value}.json`;
      a.click();
      
      URL.revokeObjectURL(url);
      showExportModal.value = false;
    };

    const copyThemeConfig = async () => {
      const config = enhancedThemeManager.exportTheme();
      config.name = exportThemeName.value;
      
      try {
        await navigator.clipboard.writeText(JSON.stringify(config, null, 2));
        // 显示成功提示
        showExportModal.value = false;
      } catch (err) {
        console.error('复制失败:', err);
      }
    };

    const importTheme = (event) => {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const config = JSON.parse(e.target.result);
          enhancedThemeManager.importTheme(config);
          
          // 更新UI状态
          currentThemeMode.value = config.theme || 'system';
          currentPreset.value = config.preset || 'default';
        } catch (err) {
          console.error('导入失败:', err);
        }
      };
      reader.readAsText(file);
    };

    const shareTheme = () => {
      const config = enhancedThemeManager.exportTheme();
      const encoded = btoa(JSON.stringify(config));
      const shareUrl = `${window.location.origin}/theme?config=${encoded}`;
      
      navigator.clipboard.writeText(shareUrl).then(() => {
        // 显示成功提示
      });
    };

    onMounted(() => {
      // 初始化当前状态
      currentThemeMode.value = enhancedThemeManager.getTheme();
      currentPreset.value = enhancedThemeManager.getCurrentPreset();
    });

    return {
      currentThemeMode,
      currentPreset,
      showAdvancedEditor,
      showExportModal,
      exportThemeName,
      themeModes,
      themePresets: processedThemePresets,
      setThemeMode,
      applyPreset,
      resetToDefault,
      exportTheme,
      downloadTheme,
      copyThemeConfig,
      importTheme,
      shareTheme
    };
  }
};
</script>

<style scoped>
.appearance-settings {
  @apply max-w-7xl mx-auto p-6;
}

.settings-header {
  @apply mb-8;
}

.settings-content {
  @apply gap-8;
}

.settings-section {
  @apply bg-n-solid-1 border border-n-weak rounded-lg p-6;
}

.section-title {
  @apply text-lg font-semibold text-n-slate-12 mb-4;
}

/* 主题模式卡片 */
.theme-mode-card {
  @apply p-4 border border-n-weak rounded-lg hover:border-n-iris-9 transition-all duration-200;
}

.theme-mode-card.active {
  @apply border-n-iris-9 bg-n-solid-iris;
}

.mode-preview {
  @apply w-full h-20 rounded-md mb-3 overflow-hidden border;
}

.light-preview {
  @apply bg-white border-gray-200;
}

.dark-preview {
  @apply bg-gray-900 border-gray-700;
}

.system-preview {
  @apply bg-gradient-to-r from-white to-gray-900 border-gray-400;
}

.preview-header {
  @apply h-3 bg-gray-100;
}

.dark-preview .preview-header {
  @apply bg-gray-800;
}

.preview-content {
  @apply flex h-full;
}

.preview-sidebar {
  @apply w-6 bg-gray-50;
}

.dark-preview .preview-sidebar {
  @apply bg-gray-700;
}

.preview-main {
  @apply flex-1 bg-white;
}

.dark-preview .preview-main {
  @apply bg-gray-800;
}

.mode-info {
  @apply flex items-center gap-2 text-n-slate-11;
}

/* 预设卡片 */
.preset-card {
  @apply p-4 border border-n-weak rounded-lg hover:border-n-iris-9 transition-all duration-200 text-left;
}

.preset-card.active {
  @apply border-n-iris-9 bg-n-solid-iris;
}

.preset-colors {
  @apply flex gap-1 mb-3;
}

.color-dot {
  @apply w-4 h-4 rounded-full border border-white shadow-sm;
}

.preset-info h3 {
  @apply text-n-slate-12 mb-1;
}

/* 高级编辑器 */
.advanced-editor {
  @apply border border-n-weak rounded-lg p-4 bg-n-solid-2;
}

/* 预览面板 */
.preview-panel {
  @apply bg-n-solid-1 border border-n-weak rounded-lg p-6;
}

.preview-container {
  @apply border border-n-weak rounded-lg overflow-hidden;
}

.preview-app {
  @apply min-h-[400px];
}

.preview-header {
  @apply border-b;
}

.preview-content {
  @apply min-h-[350px];
}

.preview-sidebar {
  @apply w-48 min-h-full;
}

.preview-main {
  @apply min-h-full;
}

/* 导出弹窗 */
.export-modal {
  @apply fixed inset-0 z-50 flex items-center justify-center;
}

.modal-backdrop {
  @apply absolute inset-0 bg-black bg-opacity-50;
}

.modal-content {
  @apply relative bg-n-solid-1 rounded-lg shadow-lg max-w-md w-full mx-4;
}

.modal-header {
  @apply flex justify-between items-center p-4 border-b border-n-weak;
}

.modal-body {
  @apply p-4;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .settings-content {
    @apply grid-cols-1;
  }

  .preview-panel {
    @apply order-first;
  }
}

@media (max-width: 768px) {
  .appearance-settings {
    @apply p-4;
  }

  .settings-header .flex {
    @apply flex-col gap-4 items-start;
  }

  .theme-mode-card,
  .preset-card {
    @apply p-3;
  }

  .mode-preview {
    @apply h-16;
  }

  .preview-sidebar {
    @apply w-32;
  }
}

/* 动画效果 */
.theme-mode-card,
.preset-card {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-mode-card:hover,
.preset-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 深色主题适配 */
:global(.dark) .light-preview {
  @apply opacity-75;
}

:global(.dark) .system-preview {
  @apply from-gray-900 to-white;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .theme-mode-card,
  .preset-card {
    @apply border-2;
  }

  .theme-mode-card.active,
  .preset-card.active {
    @apply border-4;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .theme-mode-card,
  .preset-card {
    transition: none;
  }

  .theme-mode-card:hover,
  .preset-card:hover {
    transform: none;
  }
}
</style>
