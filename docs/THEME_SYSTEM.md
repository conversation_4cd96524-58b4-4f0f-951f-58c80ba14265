# Chatwoot主题系统 - tweakcn集成指南

## 概述

本文档介绍了Chatwoot中集成的tweakcn主题系统，该系统基于OKLCH颜色格式和语义化变量命名，提供了强大的主题定制功能。

## 核心特性

### 1. OKLCH颜色系统
- **更好的感知均匀性**：OKLCH颜色空间提供了更自然的颜色过渡
- **更广的色域支持**：支持现代显示器的广色域
- **直观的颜色调整**：独立调整亮度、色度和色相

### 2. 语义化颜色变量
```css
/* 主要颜色 */
--primary: oklch(0.7 0.15 250);
--primary-foreground: oklch(0.98 0.02 250);

/* 次要颜色 */
--secondary: oklch(0.95 0.02 250);
--secondary-foreground: oklch(0.15 0.02 250);

/* 破坏性操作 */
--destructive: oklch(0.6 0.2 25);
--destructive-foreground: oklch(0.98 0.02 25);
```

### 3. 自动明暗主题支持
系统自动为每个颜色生成明暗两个版本，确保在不同主题下的最佳可读性。

## 使用方法

### 1. 基础使用

#### 在Vue组件中使用主题颜色
```vue
<template>
  <div class="bg-primary text-primary-foreground p-4 rounded-lg">
    <h2 class="text-card-foreground">标题</h2>
    <p class="text-muted-foreground">描述文本</p>
  </div>
</template>
```

#### 在CSS中使用
```css
.custom-button {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: 1px solid hsl(var(--border));
}

.custom-button:hover {
  background-color: hsl(var(--primary) / 0.9);
}
```

### 2. 主题切换

#### 使用ThemeToggle组件
```vue
<template>
  <ThemeToggle variant="dropdown" />
</template>

<script>
import ThemeToggle from '@/components-next/ThemeToggle/ThemeToggle.vue';

export default {
  components: {
    ThemeToggle
  }
};
</script>
```

#### 编程式主题切换
```javascript
import { enhancedThemeManager } from '@/theme/tweakcn-integration.js';

// 设置主题
enhancedThemeManager.setTheme('dark');

// 应用预设
enhancedThemeManager.applyPreset('ocean');

// 监听主题变化
document.addEventListener('theme-changed', (event) => {
  console.log('主题已切换到:', event.detail.theme);
});
```

### 3. 自定义颜色

#### 设置自定义颜色
```javascript
import { enhancedThemeManager } from '@/theme/tweakcn-integration.js';

// 设置自定义主色
enhancedThemeManager.setCustomColor(
  'brand-primary',
  'oklch(0.65 0.2 220)', // 浅色主题
  'oklch(0.6 0.2 220)'   // 深色主题
);
```

#### 使用颜色工具
```javascript
import { colorUtils } from '@/theme/tweakcn-integration.js';

// 调整现有颜色
const baseColor = 'oklch(0.7 0.15 250)';
const lighterColor = colorUtils.adjustLightness(baseColor, 0.1);
const moreVibrant = colorUtils.adjustChroma(baseColor, 0.05);
const shiftedHue = colorUtils.adjustHue(baseColor, 30);

// 生成颜色变体
const variants = colorUtils.generateVariants(baseColor);
// 结果: { 50: 'oklch(...)', 100: 'oklch(...)', ..., 950: 'oklch(...)' }
```

## 主题编辑器

### 1. 访问主题编辑器
导航到 `设置 > 外观设置` 来访问完整的主题编辑器。

### 2. 主要功能
- **实时预览**：所有更改立即在预览面板中显示
- **预设主题**：选择预定义的主题配色方案
- **高级编辑**：精确调整每个颜色的OKLCH值
- **导入/导出**：保存和分享自定义主题

### 3. 导出主题
```javascript
// 导出当前主题配置
const config = enhancedThemeManager.exportTheme();

// 配置结构
{
  "preset": "ocean",
  "theme": "dark",
  "customColors": {
    "custom-primary": {
      "light": "oklch(0.7 0.15 220)",
      "dark": "oklch(0.6 0.15 220)"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 可用的颜色变量

### 基础颜色
| 变量名 | 用途 | 示例 |
|--------|------|------|
| `--primary` | 主要操作和强调 | 按钮、链接 |
| `--secondary` | 次要操作 | 次要按钮 |
| `--destructive` | 危险操作 | 删除按钮 |
| `--muted` | 静音/禁用状态 | 禁用文本 |
| `--accent` | 强调元素 | 高亮、徽章 |

### 背景和前景
| 变量名 | 用途 |
|--------|------|
| `--background` | 页面背景 |
| `--foreground` | 主要文本 |
| `--card` | 卡片背景 |
| `--popover` | 弹出层背景 |

### 边框和输入
| 变量名 | 用途 |
|--------|------|
| `--border` | 一般边框 |
| `--input` | 输入框边框 |
| `--ring` | 焦点环 |

### 图表颜色
| 变量名 | 用途 |
|--------|------|
| `--chart-1` 到 `--chart-5` | 图表数据系列 |

### 侧边栏颜色
| 变量名 | 用途 |
|--------|------|
| `--sidebar-background` | 侧边栏背景 |
| `--sidebar-foreground` | 侧边栏文本 |
| `--sidebar-primary` | 侧边栏主色 |
| `--sidebar-accent` | 侧边栏强调色 |

## 最佳实践

### 1. 颜色选择
- 使用语义化的颜色变量而不是具体的颜色值
- 确保足够的对比度以满足可访问性要求
- 在不同主题下测试颜色效果

### 2. 性能优化
- 避免频繁的主题切换
- 使用CSS变量而不是JavaScript动态修改样式
- 预加载常用的主题预设

### 3. 可访问性
- 确保文本和背景的对比度至少为4.5:1
- 支持高对比度模式
- 提供减少动画的选项

## 故障排除

### 常见问题

#### 1. 颜色不生效
检查CSS变量是否正确定义：
```css
/* 错误 */
color: var(--primary);

/* 正确 */
color: hsl(var(--primary));
```

#### 2. 主题切换不工作
确保正确初始化主题管理器：
```javascript
import { enhancedThemeManager } from '@/theme/tweakcn-integration.js';

// 在应用启动时调用
enhancedThemeManager.init();
```

#### 3. 自定义颜色丢失
检查localStorage是否可用，并确保正确保存：
```javascript
// 检查localStorage支持
if (typeof Storage !== "undefined") {
  enhancedThemeManager.setCustomColor(...);
}
```

## 开发指南

### 1. 添加新的颜色变量
1. 在 `_next-colors.scss` 中定义CSS变量
2. 在 `tailwind.config.js` 中添加Tailwind类
3. 在 `tweakcn-integration.js` 中添加默认值
4. 更新文档和类型定义

### 2. 创建新的主题预设
```javascript
// 在 tweakcn-integration.js 中添加
export const themePresets = {
  // ... 现有预设
  myCustomTheme: {
    name: '我的主题',
    colors: {
      primary: { 
        light: 'oklch(0.65 0.2 180)', 
        dark: 'oklch(0.6 0.2 180)' 
      },
      // ... 其他颜色
    }
  }
};
```

### 3. 扩展颜色工具
```javascript
// 添加新的颜色操作函数
export const colorUtils = {
  // ... 现有函数
  
  // 新函数示例
  blendColors(color1, color2, ratio = 0.5) {
    // 实现颜色混合逻辑
  }
};
```

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 集成tweakcn主题系统
- 支持OKLCH颜色格式
- 实现主题编辑器
- 添加预设主题

---

如需更多帮助，请查看源代码或联系开发团队。
