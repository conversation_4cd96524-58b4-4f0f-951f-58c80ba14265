/**
 * tweakcn主题系统集成测试
 */

import {
  colorUtils,
  themePresets,
  enhancedThemeManager,
  generateCSSVariables
} from '../../../theme/tweakcn-integration.js';

describe('tweakcn主题系统集成', () => {
  // 设置测试环境
  beforeEach(() => {
    // 模拟DOM环境
    document.documentElement = {
      style: {
        setProperty: jest.fn(),
        getPropertyValue: jest.fn()
      },
      classList: {
        toggle: jest.fn(),
        add: jest.fn(),
        remove: jest.fn()
      },
      setAttribute: jest.fn()
    };
    
    // 模拟localStorage
    global.localStorage = {
      getItem: jest.fn(),
      setItem: jest.fn(),
      removeItem: jest.fn()
    };
    
    // 模拟matchMedia
    global.window.matchMedia = jest.fn(() => ({
      matches: false,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn()
    }));
  });

  describe('colorUtils', () => {
    test('应该正确解析OKLCH颜色字符串', () => {
      const oklchString = 'oklch(0.7 0.15 250)';
      const parsed = colorUtils.parseOKLCH(oklchString);
      
      expect(parsed).toEqual({
        l: 0.7,
        c: 0.15,
        h: 250
      });
    });

    test('应该正确创建OKLCH颜色字符串', () => {
      const oklchString = colorUtils.createOKLCH(0.7, 0.15, 250);
      expect(oklchString).toBe('oklch(0.7 0.15 250)');
    });

    test('应该正确调整亮度', () => {
      const originalColor = 'oklch(0.7 0.15 250)';
      const adjustedColor = colorUtils.adjustLightness(originalColor, 0.1);
      const parsed = colorUtils.parseOKLCH(adjustedColor);
      
      expect(parsed.l).toBe(0.8);
      expect(parsed.c).toBe(0.15);
      expect(parsed.h).toBe(250);
    });

    test('应该正确调整色度', () => {
      const originalColor = 'oklch(0.7 0.15 250)';
      const adjustedColor = colorUtils.adjustChroma(originalColor, 0.05);
      const parsed = colorUtils.parseOKLCH(adjustedColor);
      
      expect(parsed.l).toBe(0.7);
      expect(parsed.c).toBe(0.2);
      expect(parsed.h).toBe(250);
    });

    test('应该正确调整色相', () => {
      const originalColor = 'oklch(0.7 0.15 250)';
      const adjustedColor = colorUtils.adjustHue(originalColor, 30);
      const parsed = colorUtils.parseOKLCH(adjustedColor);
      
      expect(parsed.l).toBe(0.7);
      expect(parsed.c).toBe(0.15);
      expect(parsed.h).toBe(280);
    });

    test('应该正确处理色相环绕', () => {
      const originalColor = 'oklch(0.7 0.15 350)';
      const adjustedColor = colorUtils.adjustHue(originalColor, 30);
      const parsed = colorUtils.parseOKLCH(adjustedColor);
      
      expect(parsed.h).toBe(20); // 350 + 30 = 380, 380 % 360 = 20
    });

    test('应该生成正确的颜色变体', () => {
      const baseColor = 'oklch(0.7 0.15 250)';
      const variants = colorUtils.generateVariants(baseColor);
      
      expect(Object.keys(variants)).toHaveLength(11);
      expect(variants).toHaveProperty('50');
      expect(variants).toHaveProperty('500');
      expect(variants).toHaveProperty('950');
      
      // 检查亮度递减
      const parsed50 = colorUtils.parseOKLCH(variants[50]);
      const parsed950 = colorUtils.parseOKLCH(variants[950]);
      
      expect(parsed50.l).toBeGreaterThan(parsed950.l);
    });
  });

  describe('themePresets', () => {
    test('应该包含所有预设主题', () => {
      const expectedPresets = ['default', 'ocean', 'forest', 'sunset', 'violet'];
      
      expectedPresets.forEach(preset => {
        expect(themePresets).toHaveProperty(preset);
        expect(themePresets[preset]).toHaveProperty('name');
        expect(themePresets[preset]).toHaveProperty('colors');
      });
    });

    test('每个预设应该包含必要的颜色', () => {
      const requiredColors = ['primary', 'secondary', 'destructive', 'background'];
      
      Object.values(themePresets).forEach(preset => {
        requiredColors.forEach(color => {
          expect(preset.colors).toHaveProperty(color);
          expect(preset.colors[color]).toHaveProperty('light');
          expect(preset.colors[color]).toHaveProperty('dark');
        });
      });
    });
  });

  describe('enhancedThemeManager', () => {
    test('应该正确初始化主题', () => {
      localStorage.getItem.mockReturnValue('dark');
      
      enhancedThemeManager.init();
      
      expect(localStorage.getItem).toHaveBeenCalledWith('chatwoot-theme');
      expect(document.documentElement.classList.toggle).toHaveBeenCalledWith('dark', true);
    });

    test('应该正确设置主题', () => {
      enhancedThemeManager.setTheme('dark');
      
      expect(document.documentElement.setAttribute).toHaveBeenCalledWith('data-theme', 'dark');
      expect(document.documentElement.classList.toggle).toHaveBeenCalledWith('dark', true);
      expect(localStorage.setItem).toHaveBeenCalledWith('chatwoot-theme', 'dark');
    });

    test('应该正确处理系统主题', () => {
      window.matchMedia.mockReturnValue({
        matches: true,
        addEventListener: jest.fn(),
        removeEventListener: jest.fn()
      });
      
      enhancedThemeManager.setTheme('system');
      
      expect(document.documentElement.setAttribute).toHaveBeenCalledWith('data-theme', 'dark');
      expect(document.documentElement.classList.toggle).toHaveBeenCalledWith('dark', true);
    });

    test('应该正确应用预设主题', () => {
      enhancedThemeManager.applyPreset('ocean');
      
      expect(localStorage.setItem).toHaveBeenCalledWith('chatwoot-theme-preset', 'ocean');
      expect(document.documentElement.style.setProperty).toHaveBeenCalled();
    });

    test('应该正确设置自定义颜色', () => {
      const lightColor = 'oklch(0.8 0.1 200)';
      const darkColor = 'oklch(0.3 0.1 200)';
      
      enhancedThemeManager.setCustomColor('custom-primary', lightColor, darkColor);
      
      expect(document.documentElement.style.setProperty).toHaveBeenCalledWith('--custom-primary', lightColor);
      expect(localStorage.setItem).toHaveBeenCalledWith(
        'chatwoot-custom-colors',
        JSON.stringify({
          'custom-primary': { light: lightColor, dark: darkColor }
        })
      );
    });

    test('应该正确导出主题配置', () => {
      localStorage.getItem.mockImplementation((key) => {
        if (key === 'chatwoot-theme-preset') return 'ocean';
        if (key === 'chatwoot-theme') return 'dark';
        if (key === 'chatwoot-custom-colors') return '{"test": {"light": "oklch(0.7 0.1 100)", "dark": "oklch(0.3 0.1 100)"}}';
        return null;
      });
      
      const exported = enhancedThemeManager.exportTheme();
      
      expect(exported).toHaveProperty('preset', 'ocean');
      expect(exported).toHaveProperty('theme', 'dark');
      expect(exported).toHaveProperty('customColors');
      expect(exported).toHaveProperty('timestamp');
      expect(exported.customColors).toHaveProperty('test');
    });

    test('应该正确导入主题配置', () => {
      const config = {
        preset: 'forest',
        theme: 'light',
        customColors: {
          'test-color': { light: 'oklch(0.8 0.1 150)', dark: 'oklch(0.2 0.1 150)' }
        }
      };
      
      enhancedThemeManager.importTheme(config);
      
      expect(localStorage.setItem).toHaveBeenCalledWith('chatwoot-theme-preset', 'forest');
      expect(localStorage.setItem).toHaveBeenCalledWith('chatwoot-theme', 'light');
      expect(document.documentElement.style.setProperty).toHaveBeenCalled();
    });
  });

  describe('generateCSSVariables', () => {
    test('应该生成正确的CSS变量', () => {
      const variables = generateCSSVariables('light');
      
      expect(variables).toHaveProperty('--primary');
      expect(variables).toHaveProperty('--secondary');
      expect(variables).toHaveProperty('--destructive');
      expect(variables).toHaveProperty('--background');
      
      // 检查变量值格式
      Object.values(variables).forEach(value => {
        expect(typeof value).toBe('string');
        expect(value).toMatch(/oklch\(/);
      });
    });

    test('应该为不同主题生成不同的变量', () => {
      const lightVariables = generateCSSVariables('light');
      const darkVariables = generateCSSVariables('dark');
      
      expect(lightVariables['--primary']).not.toBe(darkVariables['--primary']);
      expect(lightVariables['--background']).not.toBe(darkVariables['--background']);
    });
  });

  describe('性能测试', () => {
    test('颜色解析应该在合理时间内完成', () => {
      const start = performance.now();
      
      for (let i = 0; i < 1000; i++) {
        colorUtils.parseOKLCH('oklch(0.7 0.15 250)');
      }
      
      const end = performance.now();
      const duration = end - start;
      
      expect(duration).toBeLessThan(100); // 应该在100ms内完成1000次解析
    });

    test('主题切换应该在合理时间内完成', () => {
      const start = performance.now();
      
      for (let i = 0; i < 100; i++) {
        enhancedThemeManager.setTheme(i % 2 === 0 ? 'light' : 'dark');
      }
      
      const end = performance.now();
      const duration = end - start;
      
      expect(duration).toBeLessThan(500); // 应该在500ms内完成100次主题切换
    });
  });

  describe('错误处理', () => {
    test('应该正确处理无效的OKLCH字符串', () => {
      const result = colorUtils.parseOKLCH('invalid-color');
      expect(result).toBeNull();
    });

    test('应该正确处理不存在的预设', () => {
      expect(() => {
        enhancedThemeManager.applyPreset('non-existent-preset');
      }).not.toThrow();
    });

    test('应该正确处理localStorage错误', () => {
      localStorage.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });
      
      expect(() => {
        enhancedThemeManager.setTheme('dark');
      }).not.toThrow();
    });
  });
});
