/**
 * tweakcn主题系统集成配置
 * 基于tweakcn的OKLCH颜色系统和语义化变量命名
 */

// OKLCH颜色转换工具
export const oklchToRgb = (l, c, h) => {
  // 简化的OKLCH到RGB转换（实际应用中需要更精确的转换）
  const hRad = (h * Math.PI) / 180;
  const a = c * Math.cos(hRad);
  const b = c * Math.sin(hRad);
  
  // Lab到XYZ转换
  const fy = (l + 16) / 116;
  const fx = a / 500 + fy;
  const fz = fy - b / 200;
  
  // 简化转换，实际需要更复杂的计算
  const r = Math.round(l * 2.55);
  const g = Math.round(l * 2.55);
  const bl = Math.round(l * 2.55);
  
  return `${r} ${g} ${bl}`;
};

// tweakcn兼容的主题配置
export const tweakcnThemeConfig = {
  // 基础颜色系统 - 使用OKLCH格式
  colors: {
    // 主色调
    primary: {
      light: 'oklch(0.7 0.15 250)', // 蓝色主色
      dark: 'oklch(0.6 0.15 250)',
    },
    'primary-foreground': {
      light: 'oklch(0.98 0.02 250)',
      dark: 'oklch(0.15 0.02 250)',
    },
    
    // 次要色调
    secondary: {
      light: 'oklch(0.95 0.02 250)',
      dark: 'oklch(0.2 0.02 250)',
    },
    'secondary-foreground': {
      light: 'oklch(0.15 0.02 250)',
      dark: 'oklch(0.9 0.02 250)',
    },
    
    // 破坏性操作色
    destructive: {
      light: 'oklch(0.6 0.2 25)', // 红色
      dark: 'oklch(0.55 0.2 25)',
    },
    'destructive-foreground': {
      light: 'oklch(0.98 0.02 25)',
      dark: 'oklch(0.98 0.02 25)',
    },
    
    // 边框和输入
    border: {
      light: 'oklch(0.9 0.02 250)',
      dark: 'oklch(0.25 0.02 250)',
    },
    input: {
      light: 'oklch(0.9 0.02 250)',
      dark: 'oklch(0.25 0.02 250)',
    },
    ring: {
      light: 'oklch(0.7 0.15 250)',
      dark: 'oklch(0.6 0.15 250)',
    },
    
    // 背景色
    background: {
      light: 'oklch(0.99 0.01 250)',
      dark: 'oklch(0.08 0.01 250)',
    },
    foreground: {
      light: 'oklch(0.15 0.02 250)',
      dark: 'oklch(0.9 0.02 250)',
    },
    
    // 卡片
    card: {
      light: 'oklch(0.99 0.01 250)',
      dark: 'oklch(0.1 0.01 250)',
    },
    'card-foreground': {
      light: 'oklch(0.15 0.02 250)',
      dark: 'oklch(0.9 0.02 250)',
    },
    
    // 弹出层
    popover: {
      light: 'oklch(0.99 0.01 250)',
      dark: 'oklch(0.1 0.01 250)',
    },
    'popover-foreground': {
      light: 'oklch(0.15 0.02 250)',
      dark: 'oklch(0.9 0.02 250)',
    },
    
    // 静音色调
    muted: {
      light: 'oklch(0.95 0.02 250)',
      dark: 'oklch(0.15 0.02 250)',
    },
    'muted-foreground': {
      light: 'oklch(0.5 0.02 250)',
      dark: 'oklch(0.6 0.02 250)',
    },
    
    // 强调色
    accent: {
      light: 'oklch(0.95 0.02 250)',
      dark: 'oklch(0.15 0.02 250)',
    },
    'accent-foreground': {
      light: 'oklch(0.15 0.02 250)',
      dark: 'oklch(0.9 0.02 250)',
    },
  },
  
  // 与现有Chatwoot颜色系统的映射
  chatwootMapping: {
    // 将tweakcn的语义化颜色映射到Chatwoot的n-*系统
    'n-slate-1': 'var(--background)',
    'n-slate-12': 'var(--foreground)',
    'n-iris-9': 'var(--primary)',
    'n-ruby-9': 'var(--destructive)',
    'n-solid-1': 'var(--card)',
    'n-solid-2': 'var(--muted)',
  },
  
  // 主题切换配置
  themeToggle: {
    storageKey: 'chatwoot-theme',
    attribute: 'data-theme',
    defaultTheme: 'system',
    themes: ['light', 'dark', 'system'],
  },
};

// 生成CSS变量的函数
export const generateCSSVariables = (theme = 'light') => {
  const config = tweakcnThemeConfig;
  const variables = {};
  
  Object.entries(config.colors).forEach(([key, value]) => {
    if (typeof value === 'object' && value[theme]) {
      variables[`--${key}`] = value[theme];
    }
  });
  
  return variables;
};

// 主题切换逻辑
export const themeManager = {
  init() {
    const stored = localStorage.getItem(tweakcnThemeConfig.themeToggle.storageKey);
    const theme = stored || tweakcnThemeConfig.themeToggle.defaultTheme;
    this.setTheme(theme);
  },
  
  setTheme(theme) {
    const root = document.documentElement;
    
    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      root.setAttribute(tweakcnThemeConfig.themeToggle.attribute, systemTheme);
      root.classList.toggle('dark', systemTheme === 'dark');
    } else {
      root.setAttribute(tweakcnThemeConfig.themeToggle.attribute, theme);
      root.classList.toggle('dark', theme === 'dark');
    }
    
    // 应用CSS变量
    const variables = generateCSSVariables(theme === 'system' ? 
      (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme);
    
    Object.entries(variables).forEach(([key, value]) => {
      root.style.setProperty(key, value);
    });
    
    localStorage.setItem(tweakcnThemeConfig.themeToggle.storageKey, theme);
  },
  
  getTheme() {
    return localStorage.getItem(tweakcnThemeConfig.themeToggle.storageKey) || 
           tweakcnThemeConfig.themeToggle.defaultTheme;
  },
  
  toggleTheme() {
    const current = this.getTheme();
    const themes = tweakcnThemeConfig.themeToggle.themes;
    const currentIndex = themes.indexOf(current);
    const nextIndex = (currentIndex + 1) % themes.length;
    this.setTheme(themes[nextIndex]);
  }
};

// OKLCH颜色工具函数
export const colorUtils = {
  // 解析OKLCH颜色字符串
  parseOKLCH(oklchString) {
    const match = oklchString.match(/oklch\(([^)]+)\)/);
    if (!match) return null;

    const values = match[1].split(/\s+/).map(v => parseFloat(v));
    return {
      l: values[0], // 亮度 (0-1)
      c: values[1], // 色度 (0-0.4)
      h: values[2]  // 色相 (0-360)
    };
  },

  // 创建OKLCH颜色字符串
  createOKLCH(l, c, h) {
    return `oklch(${l} ${c} ${h})`;
  },

  // 调整亮度
  adjustLightness(oklchString, delta) {
    const parsed = this.parseOKLCH(oklchString);
    if (!parsed) return oklchString;

    const newL = Math.max(0, Math.min(1, parsed.l + delta));
    return this.createOKLCH(newL, parsed.c, parsed.h);
  },

  // 调整色度
  adjustChroma(oklchString, delta) {
    const parsed = this.parseOKLCH(oklchString);
    if (!parsed) return oklchString;

    const newC = Math.max(0, Math.min(0.4, parsed.c + delta));
    return this.createOKLCH(parsed.l, newC, parsed.h);
  },

  // 调整色相
  adjustHue(oklchString, delta) {
    const parsed = this.parseOKLCH(oklchString);
    if (!parsed) return oklchString;

    const newH = (parsed.h + delta + 360) % 360;
    return this.createOKLCH(parsed.l, parsed.c, newH);
  },

  // 生成颜色变体
  generateVariants(baseColor) {
    const variants = {};
    const steps = [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950];

    steps.forEach((step, index) => {
      const lightnessFactor = (steps.length - 1 - index) / (steps.length - 1);
      const lightness = 0.1 + (lightnessFactor * 0.8); // 0.1 到 0.9

      const parsed = this.parseOKLCH(baseColor);
      if (parsed) {
        variants[step] = this.createOKLCH(lightness, parsed.c, parsed.h);
      }
    });

    return variants;
  }
};

// 主题预设管理
export const themePresets = {
  // 默认主题
  default: {
    name: '默认',
    colors: {
      primary: { light: 'oklch(0.7 0.15 250)', dark: 'oklch(0.6 0.15 250)' },
      secondary: { light: 'oklch(0.95 0.02 250)', dark: 'oklch(0.2 0.02 250)' },
      destructive: { light: 'oklch(0.6 0.2 25)', dark: 'oklch(0.55 0.2 25)' },
      background: { light: 'oklch(0.99 0.01 250)', dark: 'oklch(0.08 0.01 250)' },
    }
  },

  // 海洋主题
  ocean: {
    name: '海洋',
    colors: {
      primary: { light: 'oklch(0.65 0.2 220)', dark: 'oklch(0.6 0.2 220)' },
      secondary: { light: 'oklch(0.95 0.02 220)', dark: 'oklch(0.2 0.02 220)' },
      destructive: { light: 'oklch(0.6 0.2 25)', dark: 'oklch(0.55 0.2 25)' },
      background: { light: 'oklch(0.99 0.01 220)', dark: 'oklch(0.08 0.01 220)' },
    }
  },

  // 森林主题
  forest: {
    name: '森林',
    colors: {
      primary: { light: 'oklch(0.6 0.15 150)', dark: 'oklch(0.55 0.15 150)' },
      secondary: { light: 'oklch(0.95 0.02 150)', dark: 'oklch(0.2 0.02 150)' },
      destructive: { light: 'oklch(0.6 0.2 25)', dark: 'oklch(0.55 0.2 25)' },
      background: { light: 'oklch(0.99 0.01 150)', dark: 'oklch(0.08 0.01 150)' },
    }
  },

  // 日落主题
  sunset: {
    name: '日落',
    colors: {
      primary: { light: 'oklch(0.65 0.2 60)', dark: 'oklch(0.6 0.2 60)' },
      secondary: { light: 'oklch(0.95 0.02 60)', dark: 'oklch(0.2 0.02 60)' },
      destructive: { light: 'oklch(0.6 0.2 25)', dark: 'oklch(0.55 0.2 25)' },
      background: { light: 'oklch(0.99 0.01 60)', dark: 'oklch(0.08 0.01 60)' },
    }
  },

  // 紫罗兰主题
  violet: {
    name: '紫罗兰',
    colors: {
      primary: { light: 'oklch(0.6 0.2 300)', dark: 'oklch(0.55 0.2 300)' },
      secondary: { light: 'oklch(0.95 0.02 300)', dark: 'oklch(0.2 0.02 300)' },
      destructive: { light: 'oklch(0.6 0.2 25)', dark: 'oklch(0.55 0.2 25)' },
      background: { light: 'oklch(0.99 0.01 300)', dark: 'oklch(0.08 0.01 300)' },
    }
  }
};

// 增强的主题管理器
export const enhancedThemeManager = {
  ...themeManager,

  // 应用主题预设
  applyPreset(presetName) {
    const preset = themePresets[presetName];
    if (!preset) return;

    const currentMode = this.getCurrentMode();
    const root = document.documentElement;

    Object.entries(preset.colors).forEach(([colorName, colorValue]) => {
      const value = colorValue[currentMode] || colorValue.light;
      root.style.setProperty(`--${colorName}`, value);
    });

    // 保存当前预设
    localStorage.setItem('chatwoot-theme-preset', presetName);
  },

  // 获取当前模式（light/dark）
  getCurrentMode() {
    const theme = this.getTheme();
    if (theme === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return theme;
  },

  // 获取当前预设
  getCurrentPreset() {
    return localStorage.getItem('chatwoot-theme-preset') || 'default';
  },

  // 自定义颜色
  setCustomColor(colorName, lightValue, darkValue) {
    const root = document.documentElement;
    const currentMode = this.getCurrentMode();

    root.style.setProperty(`--${colorName}`, currentMode === 'dark' ? darkValue : lightValue);

    // 保存自定义颜色
    const customColors = JSON.parse(localStorage.getItem('chatwoot-custom-colors') || '{}');
    customColors[colorName] = { light: lightValue, dark: darkValue };
    localStorage.setItem('chatwoot-custom-colors', JSON.stringify(customColors));
  },

  // 导出当前主题配置
  exportTheme() {
    const preset = this.getCurrentPreset();
    const customColors = JSON.parse(localStorage.getItem('chatwoot-custom-colors') || '{}');
    const theme = this.getTheme();

    return {
      preset,
      customColors,
      theme,
      timestamp: new Date().toISOString()
    };
  },

  // 导入主题配置
  importTheme(config) {
    if (config.preset && themePresets[config.preset]) {
      this.applyPreset(config.preset);
    }

    if (config.customColors) {
      Object.entries(config.customColors).forEach(([colorName, colorValue]) => {
        this.setCustomColor(colorName, colorValue.light, colorValue.dark);
      });
    }

    if (config.theme) {
      this.setTheme(config.theme);
    }
  }
};

// 导出配置供Tailwind使用
export const tailwindThemeExtension = {
  colors: {
    // tweakcn语义化颜色
    primary: 'hsl(var(--primary))',
    'primary-foreground': 'hsl(var(--primary-foreground))',
    secondary: 'hsl(var(--secondary))',
    'secondary-foreground': 'hsl(var(--secondary-foreground))',
    destructive: 'hsl(var(--destructive))',
    'destructive-foreground': 'hsl(var(--destructive-foreground))',
    muted: 'hsl(var(--muted))',
    'muted-foreground': 'hsl(var(--muted-foreground))',
    accent: 'hsl(var(--accent))',
    'accent-foreground': 'hsl(var(--accent-foreground))',
    popover: 'hsl(var(--popover))',
    'popover-foreground': 'hsl(var(--popover-foreground))',
    card: 'hsl(var(--card))',
    'card-foreground': 'hsl(var(--card-foreground))',
    border: 'hsl(var(--border))',
    input: 'hsl(var(--input))',
    background: 'hsl(var(--background))',
    foreground: 'hsl(var(--foreground))',
    ring: 'hsl(var(--ring))',
  },
};
